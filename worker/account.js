export default {
  async fetch(request, env, ctx) {
    // CORS 配置
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400',
    };

    // 处理 CORS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: corsHeaders,
      });
    }

    try {
      // 解析 URL 参数
      const url = new URL(request.url);
      const token = url.searchParams.get('token');

      // 检查是否提供了 token 参数
      if (!token) {
        return new Response(JSON.stringify({ 
          success: false, 
          error: 'Missing token parameter' 
        }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders,
          },
        });
      }

      const notionApiToken = '**************************************************';
      const databaseId = '1e9afac028d3809c8451f1980a0f803c';
      const notionApiUrl = `https://api.notion.com/v1/databases/${databaseId}/query`;

      const headers = {
        "Authorization": `Bearer ${notionApiToken}`,
        "Content-Type": "application/json",
        "Notion-Version": "2022-06-28",
      };

      // 发起请求
      const notion_response = await fetch(notionApiUrl, {
        method: 'POST',
        headers: headers,
      });

      if (!notion_response.ok) {
        return new Response(JSON.stringify({ 
          success: false, 
          error: 'Failed to fetch data from Notion API',
          status: notion_response.status 
        }), {
          status: notion_response.status,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders,
          },
        });
      }

      const data = await notion_response.json();
      const results = data.results;

      // 查找匹配的 token
      const matchedResult = results.find(result => {
        const nameProperty = result.properties.Email;
        if (nameProperty && nameProperty.title && nameProperty.title.length > 0) {
          const name = nameProperty.title[0].text.content;
          return name === token;
        }
        return false;
      });

      if (!matchedResult) {
        return new Response(JSON.stringify({ 
          success: true, 
          token: token,
          result: null,
          message: 'Token not found in database'
        }), {
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders,
          },
        });
      }

      // 获取 account 字段
      let accountValue = "";
      const accountProperty = matchedResult.properties.account;
      if (accountProperty && accountProperty.rich_text && accountProperty.rich_text.length > 0) {
        accountValue = accountProperty.rich_text[0].plain_text;
      }

      // 获取 useage 字段
      let balanceValue = "";
      const balanceProperty = matchedResult.properties.useage;
      if (balanceProperty && balanceProperty.rich_text && balanceProperty.rich_text.length > 0) {
        balanceValue = balanceProperty.rich_text[0].plain_text;
      }

      // 获取 time 字段
      let timeValue = "";
      const timeProperty = matchedResult.properties.time;
      if (timeProperty) {
        // 2025-07-17T11:44:00.000Z 转换成 2025-07-27 的格式，只保留到日期
        timeValue = new Date(timeProperty.created_time).toISOString().split('T')[0];
      }

      // 根据 token 值决定返回哪个字段
      let resultValue = null;
      
      if (token === 'o2djU4f8pypm') {
        // 特殊 token，返回 id 字段
        const idProperty = matchedResult.properties.id;
        if (idProperty && idProperty.rich_text && idProperty.rich_text.length > 0) {
          resultValue = idProperty.rich_text[0].plain_text;
        }
      } else {
        // 普通 token，返回 date 字段
        const dateProperty = matchedResult.properties.date;
        if (dateProperty && dateProperty.rich_text && dateProperty.rich_text.length > 0) {
          resultValue = dateProperty.rich_text[0].plain_text;
        }
      }

      return new Response(JSON.stringify({ 
        success: true, 
        time:timeValue,
        token: token,
        result: resultValue,
        account: accountValue,
        useage: balanceValue

      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      });

    } catch (error) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: error.message 
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      });
    }
  },
};