// ==UserScript==
// @name              augment code tools
// @namespace         http://tampermonkey.net/
// @version           1.7.0
// @description       自动注册 augment 账号
// @icon              https://app.augmentcode.com/favicon.ico
// @name         AutoRegister for augment
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Automates registration on augmentcode.com with random email and password
// <AUTHOR> Coding
// @include      *cursor.*
// @grant        none
// ==/UserScript==
// https://checkout.stripe.com/c/pa* document.querySelector('[aria-label="用支付宝支付"]')?.click()
(function() {
    'use strict';


    async function client_id(){
        try {
            console.log('开始执行 client_id 方法');

            // 发送请求获取数据
            const response = await fetch('https://api.12050231.xyz/api/cursor', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('API 响应:', result);

            if (!result.data) {
                throw new Error('API 响应中没有 data 字段');
            }

            const dataValue = result.data;
            console.log('获取到的 data 值:', dataValue);

            // 复制到剪贴板
            try {
                await navigator.clipboard.writeText(dataValue);
                console.log('数据已复制到剪贴板:', dataValue);

                // 显示复制成功提示
                createToast(`数据已复制到剪贴板: ${dataValue}`, 'success', 3000);
            } catch (clipboardError) {
                console.warn('复制到剪贴板失败，尝试备用方法:', clipboardError);

                // 备用复制方法
                try {
                    const textArea = document.createElement('textarea');
                    textArea.value = dataValue;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);

                    console.log('使用备用方法复制到剪贴板成功:', dataValue);
                    createToast(`数据已复制到剪贴板: ${dataValue}`, 'success', 3000);
                } catch (fallbackError) {
                    console.error('所有复制方法都失败了:', fallbackError);
                    createToast(`复制失败，请手动复制: ${dataValue}`, 'warning', 5000);
                }
            }

            // 查找目标输入框
            const inputElement = document.querySelector('input.rt-reset.rt-TextFieldInput');

            if (!inputElement) {
                throw new Error('未找到目标输入框 (class: rt-reset rt-TextFieldInput)');
            }

            console.log('找到目标输入框:', inputElement);

            // 清空输入框并设置新值
            inputElement.focus();
            inputElement.value = '';

            // 使用优化的输入方法设置值
            await setInputValue(inputElement, dataValue);

            console.log(`已将值 "${dataValue}" 填入输入框`);

            // 延迟 1000ms 后点击提交按钮
            setTimeout(() => {
                const submitButton = document.querySelector('button[type="submit"]');

                if (submitButton) {
                    console.log('找到提交按钮，准备点击:', submitButton);

                    // 模拟完整的点击事件
                    simulateButtonClick(submitButton);

                    console.log('已模拟点击提交按钮');
                } else {
                    console.error('未找到 type="submit" 的按钮');
                }
            }, 1000);

        } catch (error) {
            console.error('client_id 方法执行失败:', error);

            // 显示错误提示
            createToast(`获取数据失败: ${error.message}`, 'error', 5000);
        }
    }

    async function password(){
        try {
            console.log('开始执行 password 方法');

            // 延迟 1500 毫秒后执行点击操作
            setTimeout(() => {
                try {
                    // 查找目标元素 (CSS 选择器中冒号需要转义)
                    const targetElement = document.querySelector('.ak-AuthButton');

                    if (!targetElement) {
                        console.error('未找到目标元素 (class: ak-AuthButton)');
                        createToast('未找到目标按钮元素', 'error', 5000);
                        return;
                    }

                    console.log('找到目标元素:', targetElement);
                    console.log('元素文本内容:', targetElement.textContent);

                    // 模拟点击操作
                    simulateElementClick(targetElement);

                    console.log('已模拟点击目标元素');

                } catch (error) {
                    console.error('点击操作执行失败:', error);
                    createToast(`点击操作失败: ${error.message}`, 'error', 5000);
                }
            }, 1000);

            console.log('password 方法已设置延迟执行，将在 1500ms 后点击目标元素');

        } catch (error) {
            console.error('password 方法执行失败:', error);
            createToast(`password 方法失败: ${error.message}`, 'error', 5000);
        }
    }

    // 模拟元素点击的通用函数
    function simulateElementClick(element) {
        // 确保元素可见和可点击
        if (element.style.display === 'none' || element.style.visibility === 'hidden') {
            console.warn('目标元素不可见，尝试点击可能失败');
        }

        // 滚动到元素位置
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center'
        });

        // 聚焦到元素（如果可聚焦）
        if (element.tabIndex >= 0 || element.tagName === 'BUTTON' || element.tagName === 'A') {
            element.focus();
        }

        // 创建完整的点击事件序列
        const events = [
            new MouseEvent('mouseenter', {
                bubbles: true,
                cancelable: true,
                view: window
            }),
            new MouseEvent('mouseover', {
                bubbles: true,
                cancelable: true,
                view: window
            }),
            new MouseEvent('mousedown', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0,
                buttons: 1
            }),
            new MouseEvent('mouseup', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0,
                buttons: 0
            }),
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0,
                detail: 1
            }),
            new MouseEvent('mouseleave', {
                bubbles: true,
                cancelable: true,
                view: window
            })
        ];

        // 依次触发所有事件
        events.forEach((event, index) => {
            setTimeout(() => {
                try {
                    const result = element.dispatchEvent(event);
                    console.log(`事件 ${event.type} 触发${result ? '成功' : '失败'}`);
                } catch (e) {
                    console.warn(`事件 ${event.type} 触发失败:`, e);
                }
            }, index * 10); // 每个事件间隔 10ms
        });

        // 如果是链接，尝试导航
        if (element.tagName === 'A' && element.href) {
            setTimeout(() => {
                try {
                    window.location.href = element.href;
                } catch (e) {
                    console.warn('链接导航失败:', e);
                }
            }, 100);
        }

        // 如果元素在表单中，尝试提交表单
        const form = element.closest('form');
        if (form && (element.type === 'submit' || element.tagName === 'BUTTON')) {
            setTimeout(() => {
                try {
                    form.submit();
                } catch (e) {
                    console.warn('表单提交失败:', e);
                }
            }, 100);
        }
    }

    // 设置输入框值的辅助函数
    async function setInputValue(inputElement, value) {
        // 聚焦到输入框
        inputElement.focus();

        // 清空现有值
        inputElement.value = '';

        // 使用原生值描述符设置值
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
            window.HTMLInputElement.prototype,
            'value'
        ).set;

        if (nativeInputValueSetter) {
            nativeInputValueSetter.call(inputElement, value);
        }

        // 设置值
        inputElement.value = value;

        // 触发完整的事件序列
        const events = [
            new Event('focus', { bubbles: true }),
            new Event('input', {
                bubbles: true,
                cancelable: true,
                data: value,
                inputType: 'insertText'
            }),
            new Event('change', { bubbles: true }),
            new Event('blur', { bubbles: true })
        ];

        // 依次触发所有事件
        events.forEach(event => {
            try {
                inputElement.dispatchEvent(event);
            } catch (e) {
                console.warn('事件触发失败:', e);
            }
        });

        // 强制更新框架状态
        forceFrameworkUpdate(inputElement, value);

        // 设置属性确保值被保存
        inputElement.setAttribute('value', value);
    }

    // 模拟按钮点击的辅助函数
    function simulateButtonClick(buttonElement) {
        // 聚焦到按钮
        buttonElement.focus();

        // 创建完整的点击事件序列
        const events = [
            new MouseEvent('mousedown', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0
            }),
            new MouseEvent('mouseup', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0
            }),
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0
            })
        ];

        // 依次触发所有事件
        events.forEach(event => {
            try {
                buttonElement.dispatchEvent(event);
            } catch (e) {
                console.warn('按钮点击事件触发失败:', e);
            }
        });

        // 如果按钮有表单，尝试提交表单
        const form = buttonElement.closest('form');
        if (form) {
            try {
                form.submit();
            } catch (e) {
                console.warn('表单提交失败:', e);
            }
        }
    }


    // 等待页面加载完成
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                } else {
                    setTimeout(check, 100);
                }
            }
            
            check();
        });
    }
    
    // 提取验证码的函数（参考zhuce.js中的实现）
    function extractVerificationCode(text) {
        // 首先尝试匹配 6 位数字验证码（兼容空格）
        const digitMatch = text.match(/\b\d(?:\s\d){5}\b|\b\d{6}\b/);
        if (digitMatch) {
            return digitMatch[0].replace(/\s/g, '');
        }

        // 降级：尝试匹配 4-8 位的字母数字组合验证码（兼容空格）
        const alphanumericMatch = text.match(/\b[A-Z0-9](?:\s[A-Z0-9]){3,7}\b|\b[A-Z0-9]{4,8}\b/);
        if (alphanumericMatch) {
            return alphanumericMatch[0].replace(/\s/g, '');
        }

        return null;
    }
    
    // 获取当前邮箱的函数
    function getCurrentEmail() {
        // 尝试多种可能的邮箱选择器
        const emailSelectors = [
            '.rt-Text.rt-r-weight-bold',
        ];
        
        for (const selector of emailSelectors) {
            const emailElement = document.querySelector(selector);
            if (emailElement) {
                const email = emailElement.textContent?.trim() || emailElement.value?.trim();
                return email;
            }
        }
        
        // 如果找不到邮箱元素，尝试从URL或其他地方获取
        console.warn('无法自动获取邮箱，请手动指定');
        return null;
    }
    
    // 填充OTP到输入框的函数
    function fillOtpInputs(code) {
        const otpInputs = document.querySelectorAll('.rt-TextFieldRoot.rt-r-size-3.rt-variant-surface.ak-TextField.ak-OtpInput input');

        if (otpInputs.length === 0) {
            console.error('未找到OTP输入框');
            return false;
        }

        console.log(`找到 ${otpInputs.length} 个OTP输入框`);

        // 将验证码字符分配到各个输入框
        for (let i = 0; i < Math.min(code.length, otpInputs.length); i++) {
            const input = otpInputs[i];
            const char = code[i];

            // 先聚焦到输入框
            input.focus();

            // 清空现有值
            input.value = '';

            // 模拟真实的键盘输入过程
            simulateKeyboardInput(input, char);

            // 设置值并触发多种事件
            input.value = char;

            // 触发完整的事件序列，模拟真实用户输入
            const events = [
                new Event('focus', { bubbles: true }),
                new KeyboardEvent('keydown', {
                    key: char,
                    code: `Digit${char}`,
                    keyCode: char.charCodeAt(0),
                    bubbles: true
                }),
                new KeyboardEvent('keypress', {
                    key: char,
                    code: `Digit${char}`,
                    keyCode: char.charCodeAt(0),
                    bubbles: true
                }),
                new Event('input', {
                    bubbles: true,
                    cancelable: true,
                    data: char,
                    inputType: 'insertText'
                }),
                new KeyboardEvent('keyup', {
                    key: char,
                    code: `Digit${char}`,
                    keyCode: char.charCodeAt(0),
                    bubbles: true
                }),
                new Event('change', { bubbles: true }),
                new Event('blur', { bubbles: true })
            ];

            // 依次触发所有事件
            events.forEach(event => {
                try {
                    input.dispatchEvent(event);
                } catch (e) {
                    console.warn('事件触发失败:', e);
                }
            });

            // 强制更新React/Vue等框架的状态
            forceFrameworkUpdate(input, char);

            // 模拟用户输入后的延迟
            if (i < code.length - 1) {
                setTimeout(() => {}, 50); // 短暂延迟模拟真实输入
            }
        }

        console.log(`验证码 ${code} 已填充到OTP输入框`);
        return true;
    }

    // 模拟键盘输入的辅助函数
    function simulateKeyboardInput(element, char) {
        // 创建更真实的输入事件
        const inputEvent = new InputEvent('input', {
            bubbles: true,
            cancelable: true,
            data: char,
            inputType: 'insertText',
            isComposing: false
        });

        // 设置原生值描述符
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
            window.HTMLInputElement.prototype,
            'value'
        ).set;

        if (nativeInputValueSetter) {
            nativeInputValueSetter.call(element, char);
        }

        element.dispatchEvent(inputEvent);
    }

    // 强制更新现代框架状态的辅助函数
    function forceFrameworkUpdate(element, value) {
        // React 特殊处理
        if (element._valueTracker) {
            element._valueTracker.setValue('');
        }

        // 查找React Fiber节点
        const reactFiber = element._reactInternalFiber ||
                          element._reactInternalInstance ||
                          Object.keys(element).find(key => key.startsWith('__reactInternalInstance'));

        if (reactFiber) {
            const props = reactFiber.memoizedProps || reactFiber.pendingProps;
            if (props && props.onChange) {
                props.onChange({
                    target: element,
                    currentTarget: element,
                    type: 'change'
                });
            }
        }

        // Vue 特殊处理
        if (element.__vue__) {
            const vueInstance = element.__vue__;
            if (vueInstance.$emit) {
                vueInstance.$emit('input', value);
                vueInstance.$emit('change', value);
            }
        }

        // 通用的属性设置
        element.setAttribute('value', value);

        // 触发 MutationObserver
        if (window.MutationObserver) {
            const observer = new MutationObserver(() => {});
            observer.observe(element, { attributes: true, attributeFilter: ['value'] });
            observer.disconnect();
        }
    }

    // 统一的验证码获取函数
    async function getVerificationCode(options = {}) {
        const {
            loadingText = '正在获取验证码...',
            autoFill = true,
            showAlert = false
        } = options;

        try {
            // 获取当前邮箱
            const currentEmail = getCurrentEmail();
            if (!currentEmail) {
                const errorMsg = '无法获取当前邮箱，请确保页面中包含邮箱信息';
                if (showAlert) {
                    alert(errorMsg);
                    return null;
                } else {
                    throw new Error(errorMsg);
                }
            }

            console.log('当前邮箱:', currentEmail);

            // 构建API URL
            const apiUrl = `https://email.767700.xyz/?email=${encodeURIComponent(currentEmail)}&token=E567OUwuWuN`;

            // 显示加载提示
            const loadingToast = createToast(loadingText, 'loading');

            // 发送请求
            const response = await fetch(apiUrl);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            const text = data.text || data;
            console.log('API 响应:', text);

            // 提取验证码
            const verificationCode = extractVerificationCode(text);

            // 移除加载提示
            loadingToast.remove();

            if (!verificationCode) {
                throw new Error('未能从邮件中提取到验证码');
            }

            console.log('提取到验证码:', verificationCode);

            // 自动填充验证码
            if (autoFill) {
                const fillSuccess = fillOtpInputs(verificationCode);

                if (fillSuccess) {
                    createToast(`验证码 ${verificationCode} 已自动填入`, 'success', 3000);
                } else {
                    const message = `验证码: ${verificationCode} (请手动输入)`;
                    if (showAlert) {
                        alert(`验证码获取成功: ${verificationCode}\n请手动输入到验证码框中`);
                    } else {
                        createToast(message, 'warning', 5000);
                    }
                }
            } else {
                createToast(`验证码获取成功: ${verificationCode}`, 'success', 5000);
            }

            return verificationCode;

        } catch (error) {
            console.error('获取验证码失败:', error);

            // 移除可能存在的加载提示
            const existingToast = document.querySelector(`div[style*="${loadingText}"]`);
            if (existingToast) {
                existingToast.remove();
            }

            createToast(`获取验证码失败: ${error.message}`, 'error', 5000);
            return null;
        }
    }

    // 创建提示框的统一函数
    function createToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.textContent = message;

        const colors = {
            loading: { bg: '#007bff', color: '#fff' },
            success: { bg: '#28a745', color: '#fff' },
            error: { bg: '#dc3545', color: '#fff' },
            warning: { bg: '#ffc107', color: '#212529' },
            info: { bg: '#17a2b8', color: '#fff' }
        };

        const colorScheme = colors[type] || colors.info;

        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            backgroundColor: colorScheme.bg,
            color: colorScheme.color,
            padding: '12px 20px',
            borderRadius: '6px',
            fontSize: '14px',
            fontWeight: 'bold',
            zIndex: '10000',
            boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
            maxWidth: '400px',
            textAlign: 'center'
        });

        document.body.appendChild(toast);

        if (duration > 0) {
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, duration);
        }

        return toast;
    }

    // 主要的处理函数（保持向后兼容）
    async function handleOtpRequest() {
        await getVerificationCode({
            loadingText: '正在获取验证码...',
            autoFill: true,
            showAlert: true
        });
    }
    
    // 增强的点击事件处理函数
    function enhancedClickHandler(event) {
        // 阻止默认行为和事件冒泡
        event.preventDefault();
        event.stopPropagation();

        console.log('点击事件被触发');

        // 调用主处理函数
        handleOtpRequest();
    }

    // Magic Code 自动获取验证码方法
    async function magiccode() {
        console.log('开始执行 magiccode 方法 - 自动获取验证码');
        await getVerificationCode({
            loadingText: '正在自动获取验证码...',
            autoFill: true,
            showAlert: false
        });
    }

    async function trial(){
        try {
            console.log('开始执行 trial 方法 - 延迟点击按钮');

            // 延迟 1500 毫秒后执行点击操作
            setTimeout(() => {
                try {
                    // 查找目标按钮
                    const targetButton = document.querySelector('button.text-black');

                    if (!targetButton) {
                        console.error('未找到目标按钮 (class: text-black)');
                        createToast('未找到目标按钮元素', 'error', 5000);
                        return;
                    }

                    console.log('找到目标按钮:', targetButton);
                    console.log('按钮文本内容:', targetButton.textContent);

                    // 模拟点击操作
                    simulateElementClick(targetButton);

                    console.log('已模拟点击目标按钮');
                    createToast('已自动点击按钮', 'success', 3000);

                } catch (error) {
                    console.error('点击操作执行失败:', error);
                    createToast(`点击操作失败: ${error.message}`, 'error', 5000);
                }
            }, 1500);

            console.log('trial 方法已设置延迟执行，将在 1500ms 后点击目标按钮');

        } catch (error) {
            console.error('trial 方法执行失败:', error);
            createToast(`trial 方法失败: ${error.message}`, 'error', 5000);
        }
    }
    
    const url = window.location.href;

  function init() {
    if (url.includes("/?client_id=client")) {
      client_id();
    } else if (url.includes("password?email")) {
      password();
    } else if(url.includes("magic-code?email")){
        setTimeout(() => {
            magiccode();
        }, 3000);
      
    } else if (url.includes("cursor.com/trial")) {
      trial();
    }else if (url.includes("account/team")) {
      newteam();
    }else if (url.includes("promotions/windsurf")) {
      windsurf();
    }
  }

    // 页面加载完成后创建 UI
setTimeout(() => {
    init();
}, 500);
    
})();



// 查找具有 aria-label="用支付宝支付" 的元素并模拟点击
function clickAlipayButton() {
    // 使用属性选择器查找元素
    const alipayButton = document.querySelector('[aria-label="用支付宝支付"]');
    
    if (alipayButton) {
        // 检查元素是否可见和可点击
        if (alipayButton.offsetParent !== null && !alipayButton.disabled) {
            // 模拟点击事件
            alipayButton.click();
            console.log('支付宝支付按钮已被点击');
            return true;
        } else {
            console.log('支付宝支付按钮存在但不可点击（可能被隐藏或禁用）');
            return false;
        }
    } else {
        console.log('未找到 aria-label="用支付宝支付" 的元素');
        return false;
    }
}

// 执行点击操作
clickAlipayButton();

// 或者更简洁的单行版本：
// document.querySelector('[aria-label="用支付宝支付"]')?.click();