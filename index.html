<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="https://www.ppanda.com/images/logo-frog-all.png" type="image/x-icon">
    <title>Cursor 邮箱验证码临时查询</title>
    <style>
        :root {
            --primary-color: #4a6bdf;
            --secondary-color: #f8f9fa;
            --text-color: #333;
            --error-color: #dc3545;
            --success-color: #28a745;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f5f7fa;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            min-height: 100vh;
            /* padding: 20px; */
        }

        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 700px;
            padding: 30px;
            transition: all 0.3s ease;
        }

        .container:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        h1 {
            text-align: center;
            margin-bottom: 25px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        input[type="email"] {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="email"]:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.2);
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #3a56b8;
        }

        button:disabled {
            background-color: #a0aec0;
            cursor: not-allowed;
        }

        .result {
            margin-top: 25px;
            padding: 15px;
            border-radius: 6px;
            background-color: var(--secondary-color);
            display: none;
        }

        .result.show {
            display: block;
        }

        .result h3 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .result p {
            margin-bottom: 5px;
        }

        .error {
            color: var(--error-color);
        }

        .success {
            color: var(--success-color);
        }

        .loader {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loader.show {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .tips {
            margin: 20px;
            text-align: center;

        }

        .tips img {
            width: 270px;
            margin-top: 20px;
            border-radius: 10px;
        }

     
    </style>
</head>
<body>


    <!-- <div class="tips"><img src="https://img.26272829.xyz/images/2025-05-19-QLHrrBIMG_2880.JPG" alt=""></div> -->
    <!-- <div class="tips"><img src="https://img.26272829.xyz/images/2025-04-27-qCFmmmrGM95p.png" alt=""></div> -->
    <!-- <div class="tips"><img src="https://img.262700.xyz/images/2025-06-19-Oj1sOlEpTZqu.png" alt=""></div> -->
    <div class="container">
        <h1><img src="https://www.ppanda.com/images/logo-frog-all.png" width="30px" alt=""> Cursor 验证码查询</h1>
        <!-- <div style="text-align: center;margin: 10px;color: red;font-size: 14px;">账号被禁言了，后续有需求可以搜索用户<b style="color: #4a6bdf;"> “自信的蛙蛙” </b>客服+购买，感谢支持。</div> -->
        <form id="emailForm">
            <div class="form-group">
                <label for="email">请输入邮箱地址获取验证码<span style="color: red;"> ↓ 下面使用说明必读</span></label></label>
                <input type="email" id="email" name="email" placeholder="例如: <EMAIL>" required>
            </div>
            <!-- <p style="color: red;margin-bottom: 7px;">登录后先点击软件右上角齿轮查看左上角账号权限，显示为 Pro Trial 为正常新账号</p> -->
            <button type="submit" id="submitBtn">查询</button>

        </form>

        <div class="loader" id="loader">
            <div class="spinner"></div>
            <p>正在查询中，链路较长，请耐心等待或稍后重试...</p>
            <p class="quote" style="margin-top: 15px; font-style: italic; color: #666;"></p>
        </div>

        <div class="result" id="result">
            <h3>查询结果</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <div class="tips-container zhongyao">
        <div class="steps-header">
            <h2>Pro trial 14 天 使用步骤指南（官方新规则！不看不能用）</h2>
            <p>按照以下步骤完成操作，否则不是 Pro trial 14 天</p>
        </div>
        <div class="steps-container">
            <div class="step-item" data-step="1">
                <div class="step-number">1</div>
                <div class="step-content">
                    <img src="https://img.262700.xyz/images/2025-07-25-DYWPxlvcE7fO.png" alt="步骤1">
                    <div class="step-description">第一步操作说明</div>
                </div>
            </div>
            <div class="step-item" data-step="2">
                <div class="step-number">2</div>
                <div class="step-content">
                    <img src="https://img.262700.xyz/images/2025-07-25-HTbezs1qY72H.png" alt="步骤2">
                    <div class="step-description">第二步操作说明</div>
                </div>
            </div>
            <div class="step-item" data-step="3">
                <div class="step-number">3</div>
                <div class="step-content">
                    <img src="https://img.262700.xyz/images/2025-07-25-0z1Xv2wZUnyb.png" alt="步骤3">
                    <div class="step-description">第三步操作说明</div>
                </div>
            </div>
            <div class="step-item" data-step="4">
                <div class="step-number">4</div>
                <div class="step-content">
                    <img src="https://img.262700.xyz/images/2025-07-25-X8nufWYsfBIm.png" alt="步骤4">
                    <div class="step-description">第四步操作说明</div>
                </div>
            </div>
            <div class="step-item" data-step="5">
                <div class="step-number">5</div>
                <div class="step-content">
                    <img src="https://img.262700.xyz/images/2025-07-25-6iv67RSHff3I.png" alt="步骤5">
                    <div class="step-description">第五步操作说明</div>
                </div>
            </div>
        </div>
    </div>


    <div class="tips-container">
        <!-- <div style="text-align: center;margin: 10px;color: red;font-size: 14px;"><b style="color: #4a6bdf;">带刀的猫咪</b>被禁言，后续有需求可以搜索用户<b style="color: #4a6bdf;"> “自信的蛙蛙” </b>客服+购买，不聊魔法，感谢支持。</div> -->
        <!-- <div class="tips"><img src="https://img.262700.xyz/images/2025-06-19-Oj1sOlEpTZqu.png" alt=""></div> -->
        <div style="text-align: center;margin: 10px;color: red;font-size: 14px;">登录后及时查看官网信息是不是 Pro trial 14 天，官网是不会骗人的。</div>
        <div style="text-align: center;"><img style="border-radius: 10px;" src="https://img.1953615.xyz/m1/2025-07-26-20-57-ckNIDT.png" width="600px" alt=""></div>
        <div class="tips-card">
            <h2>常见问题 </h2>

            <div class="tips-content">
                <!-- <div class="tips-item tips-item-1">
                    <h3><span style="color: red;">登录后还不断跳登录，会补发新账号</span></h3>
                </div> -->

                <div class="tips-item tips-item-2">
                    <div>
                        <p>⚠️<span style="color: red;">多次使用账户前需要先重置机器码</span>，出现<code>too many free trial accounts used on this machine</code>此类问题不售后（账户没问题，换台电脑就能用），需要用户重置机器码后使用。</p>
                    </div>
                </div>
                
                
                <div class="tips-item tips-item-7">
                    <div class="hint">
                        <strong style="color: #dc3545;">Windows 重置机器码方法（在 PowerShell 输入以下命令）：</strong>
                        <p><code>irm https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_win_id_modifier.ps1 | iex</code>
                        </p>
                
                        <p><strong>如何打开管理员 PowerShell：</strong></p>
                        <ol style="margin: 8px 0; padding-left: 20px;">
                            <li>按下 Win + X 快捷键</li>
                            <li>在弹出的菜单中选择以下任一选项:
                                <ul style="margin: 4px 0;">
                                    <li>Windows PowerShell (管理员)</li>
                                    <li>Windows Terminal (管理员)</li>
                                    <li>终端(管理员)</li>
                                </ul>
                            </li>
                        </ol>
                        <em>注：具体选项可能因Windows版本而异</em>
                
                        <p style="color: red;">如果重置机器码后提示路径错误（一般都是用户自己定义了类似下图路径），这时候需要自己使用页尾 token 方案自己修改路径。或者自己重装 Cursor 到 Program
                            Files 目录路径</p>
                        <img src="https://img.alicdn.com/imgextra/i3/2219812916801/O1CN01G1V9M1206uiiMb6ni_!!2219812916801-2-xy_chat.png"
                            width="650px" alt="">
                        </p>
                    </div>
                
                </div>
                
                <div class="tips-item tips-item-8">
                    <div class="hint">
                        <strong style="color: #dc3545;">Mac 重置机器码方法（在 终端 输入以下命令）：</strong>
                        <p><code>curl -fsSL
                                                        https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_mac_id_modifier.sh
                                                        -o ./cursor_mac_id_modifier.sh && sudo bash ./cursor_mac_id_modifier.sh && rm ./cursor_mac_id_modifier.sh</code>
                        </p>
                    </div>
                </div>
                <div class="tips-item tips-item-8">
                    <div class="hint">
                        <strong style="color: #dc3545;">Linux 重置机器码方法（在 终端 输入以下命令）：</strong>
                        <p><code>curl -fsSL https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_linux_id_modifier.sh | sudo bash</code>PS：Linux
                            把cursor app安装到/Applications目录下才能执行脚本</p>
                    </div>
                </div>
                <div class="tips-item tips-item-6">
                    <div>
                        <strong style="color: red;">Cursor通用解决方案（包括掉试用pro）：
                            换浏览器、换IP、删除 `用户\.cursor` 文件下面所有文件（会丢失全部设置，请手动备份）、打开Cursor、执行重置脚本即可。</strong>
                    </div>
                </div>
                <!-- <div class="tips-item tips-item-3">
                    <div>
                        <p>⚠️<span style="color: red;">尽量不要使用梯子，节点 IP 质量差会中风控，出现此类问题最多只会补一个新账号</span>，可以访问 <a target="_blank" href="https://ping0.cc/">查询 IP 风控</a></p>
                        <h4>如何排查被官方标记：<span style="color: rgb(156, 40, 186);">如果变成 Free 需要使用页尾 Token 模式</span></h4>
                        <img width="650px" src="img/SCR-20250617-omry.png"
                            alt="">
                        <img width="650px" src="img/SCR-20250617-oocf.png" alt="">
                    </div>
                </div> -->

                <div class="tips-item tips-item-4">
                    <div style="font-weight: bold;">
                        <img src="https://img.alicdn.com/imgextra/i2/2214682949688/O1CN01AfnnWK2LRAA5KMN6o_!!2214682949688-0-xy_chat.jpg" width="650px" alt="">
                        <h3>前置条件是有梯子！！！</h3>
                        <p>Cursor 锁🔒国区🇨🇳 解决方法如下👇/ <span   style="color: red;">auto 模型提示额度不足也使用此方法</span></p>
                        <p>1、右上角齿轮 - Network - 改成 HTTP1.1，代理才能生效</p>
                        <img src="https://img.262700.xyz/images/2025-07-23-a64xXaoLZiVf.png" width="550px" alt="">
                        <p>（左上角文件 - 首选项 - 设置 - 搜索🔍proxy， 设置本地魔法代理类似 127.0.0.1:7890 这种）</p>
                        <img src="https://img.1953615.xyz/m1/2025-07-22-22-50-UssRvk.png" width="550px" alt="">
                        <p>2、魔法开启 tun 模式（会写分流的可以自己写分流规则，代理设置成功的可以不需要全局，tun 模式即可）</p>
                    </div>
                </div>
                <div class="tips-item tips-item-5">
                    <div>
                        <img src="https://img.alicdn.com/imgextra/i2/1625633697/O1CN017HLQYb1dBHKXlDQKR_!!1625633697-53-xy_chat.heic" width="350px" alt="">
                        <p>Mac 重置后 Cursor 打不开？</p>
                        <p>终端输入：<pre style="margin: 10px 0;">
<code>sudo chown -R $(whoami) ~/Library/"Application Support"/Cursor
sudo chown -R $(whoami) ~/.cursor
chmod -R u+w ~/Library/"Application Support"/Cursor
chmod -R u+w ~/.cursor/extensions
</code></pre></p>
                        或者
                        <p>终端输入：
                        <pre style="margin: 10px 0;">
<code>sudo chown -R $(whoami) "/Users/<USER>/Library/Application Support/Cursor"
sudo chown -R $(whoami) "/Users/<USER>/.cursor"
</code>
                                                </pre>
                        </p>
                    </div>
                </div>

                <div class="tips-item tips-item-6">
                    <p>如果您的浏览器被目标网站识别或限制（人机识别风控）。此时，请尝试更换浏览器，例如：Edge、Google
                        Chrome、Firefox。（或者，可以尝试使用能够修改或随机化浏览器指纹信息的浏览器），详细内容可以查看 <a href="https://docs.qq.com/aio/DUE5pS0J3T1ZqTk1P?p=wo8hq9ynyufuafxNIbrwuy">Cursor常见问题</a></p>
                </div>
                
                
                

                <div class="tips-item tips-item-9">
                    <h3 style="color: #e67fea;">备用方案：Token 模式。在网络或者电脑环境被官方检测到，经常掉 Pro 试用时，可以切换 Token 模式登录。</h3>
                    <h3>加客服可帮代转 token（非实时在线）</h3>
                    <p><a  target="_blank" href="https://img.262700.xyz/images/2025-06-25-TkrTikcurs0rgo.win.zip">下载 Windows 切换 Token 登录软件</a></p>
                    <p><a  target="_blank" href="https://img.1953615.xyz/m1/2025-06-25-20-29-curs0rgo.mac.zip">下载 Mac 切换 Token 登录软件</a></p>
                    <p><a  target="_blank" href="https://img.262700.xyz/images/2025-07-24-TY9q12curs0rgo.linux.zip">下载 Linux 切换 Token 登录软件</a></p>
                    <p> <a target="_blank" href="https://token.767700.xyz/">🌟🌟🌟 如何获取 token，使用必读 🌟🌟🌟</a></p>
                    <img width="350px" src="https://img.1953615.xyz/m1/2025-06-25-21-07-0tCsel.png" alt="">

                </div>


            </div>
        </div>
    </div>

    <style>
        .tips-container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .tips-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .tips-card h2 {
            color: #333;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .tips-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .tips-item {
            gap: 15px;
            padding: 15px;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .tips-item-1 {
            background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
            border-left: 4px solid #ff6b6b;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.15);
        }

        .tips-item-2 {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left: 4px solid #0ea5e9;
            box-shadow: 0 2px 8px rgba(14, 165, 233, 0.15);
        }

        .tips-item-3 {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-left: 4px solid #64748b;
            box-shadow: 0 2px 8px rgba(100, 116, 139, 0.15);
        }

        .tips-item-4 {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border-left: 4px solid #ef4444;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15);
        }

        .tips-item-5 {
            background: linear-gradient(135deg, #fdf4ff 0%, #f3e8ff 100%);
            border-left: 4px solid #a855f7;
            box-shadow: 0 2px 8px rgba(168, 85, 247, 0.15);
        }

        .tips-item-6 {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border-left: 4px solid #22c55e;
            box-shadow: 0 2px 8px rgba(34, 197, 94, 0.15);
        }

        .tips-item-7 {
            background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
            border-left: 4px solid #eab308;
            box-shadow: 0 2px 8px rgba(234, 179, 8, 0.15);
        }

        .tips-item-8 {
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
            border-left: 4px solid #ec4899;
            box-shadow: 0 2px 8px rgba(236, 72, 153, 0.15);
        }

        .tips-item-9 {
            background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
            border-left: 4px solid #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
        }

        .tips-icon {
            font-size: 20px;
            font-style: normal;
        }

        .tips-item h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #333;
        }

        .tips-item p {
            margin: 0 0 10px 0;
            line-height: 1.6;
        }

        .tips-item ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        .tips-item li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .tips-item code {
            background-color: #f0f0f0;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }

        @media (max-width: 576px) {
            body {
                padding: 10px;
            }

            .container {
                margin: 10px;
                padding: 20px;
            }

            .tips {
                margin: 10px;
            }

            .tips img {
                width: 100%;
                max-width: 270px;
            }

            .tips-container {
                margin: 20px auto;
                padding: 0 10px;
            }

            .tips-card {
                padding: 15px;
                margin-bottom: 15px;
            }

            .tips-item {
                padding: 12px;
                gap: 10px;
                flex-direction: column;
            }

            .tips-item img {
                width: 100%;
                height: auto;
                max-width: 100%;
            }

            .tips-item h3 {
                font-size: 16px;
                line-height: 1.4;
            }

            .tips-item p {
                font-size: 14px;
                line-height: 1.5;
            }

            .tips-icon {
                align-self: flex-start;
            }
        }

        /* zhongyao 步骤样式 */
        .zhongyao {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 40px auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            display: none;
        }

        .zhongyao::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4a6bdf, #6366f1, #8b5cf6, #a855f7);
        }

        .steps-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .steps-header h2 {
            color: #1e293b;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #4a6bdf, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .steps-header p {
            color: #64748b;
            font-size: 16px;
            margin: 0;
        }

        .steps-container {
            display: flex;
            flex-direction: column;
            gap: 30px;
            position: relative;
        }

        .steps-container::before {
            content: '';
            position: absolute;
            left: 30px;
            top: 60px;
            bottom: 60px;
            width: 2px;
            background: linear-gradient(180deg, #4a6bdf, #8b5cf6);
            z-index: 1;
        }

        .step-item {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            position: relative;
            z-index: 2;
        }

        .step-number {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4a6bdf, #6366f1);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            box-shadow: 0 4px 16px rgba(74, 107, 223, 0.3);
            flex-shrink: 0;
            position: relative;
        }

        .step-number::after {
            content: '';
            position: absolute;
            inset: -3px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4a6bdf, #6366f1);
            z-index: -1;
            opacity: 0.2;
        }

        .step-content {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
        }

        .step-content:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .step-content img {
            width: 100%;
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .step-content::after {
            content: '';
            position: absolute;
            right: 20px;
            bottom: 20px;
            width: 100px;
            height: 100px;
            background-image: url('https://www.ppanda.com/images/logo-frog-all.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            pointer-events: none;
            z-index: 1;
        }

        .step-description {
            color: #475569;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .zhongyao {
                margin: 20px auto;
                padding: 20px;
            }

            .steps-header h2 {
                font-size: 24px;
            }

            .steps-container::before {
                left: 20px;
            }

            .step-number {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .step-content {
                padding: 15px;
            }

            .step-description {
                font-size: 14px;
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('emailForm');
            const loader = document.getElementById('loader');
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            const submitBtn = document.getElementById('submitBtn');

            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const email = document.getElementById('email').value.trim();

                if (!email) {
                    showError('请输入有效的邮箱地址');
                    return;
                }

                // 显示加载状态
                loader.classList.add('show');
                result.classList.remove('show');
                submitBtn.disabled = true;

                // 获取名言
                try {
                    const quoteResponse = await fetch('https://read.767700.xyz/');
                    const quoteData = await quoteResponse.json();
                    if (quoteData.code === 1 && quoteData.data && quoteData.data.length > 0) {
                        const quoteElement = loader.querySelector('.quote');
                        quoteElement.textContent = quoteData.data[0].value;
                    }
                } catch (error) {
                    console.error('获取名言失败:', error);
                }

                try {
                    // 从URL参数中获取token
                    const urlParams = new URLSearchParams(window.location.search);
                    const token = urlParams.get('token');
                    
                    // 构建API请求URL
                    let apiUrl = `https://email.767700.xyz/?email=${encodeURIComponent(email)}`;
                    if (token) {
                        apiUrl += `&token=${encodeURIComponent(token)}`;
                    }
                    
                    const response = await fetch(apiUrl);

                    if (!response.ok) {
                        throw new Error('未查询到相关邮件，请确认邮箱是否正确，稍后再重新查询');
                    }

                    const data = await response.json();
                    data.date = new Date(data.date).toLocaleString('zh-CN', {
                        timeZone: 'Asia/Shanghai',
                        hour12: false,  // 24小时制
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    })

                    // 隐藏加载状态
                    loader.classList.remove('show');
                    result.classList.add('show');
                    submitBtn.disabled = false;

                    if (data.result) {
                        let resultHtml = ``;

                        if (data.subject) {
                            resultHtml += `
                <h2>${data.subject}</h2>
                <div class="meta">发件人：${data.from} | 收件人：${data.to} | 时间：${data.date}</div>
            `;
                        }



                        if (data.html) {
                            resultHtml += `<p>${data.html.replace(/\n/g, '')}</p>`;
                        }
                        resultContent.innerHTML = resultHtml;
                        try {
                            // await navigator.clipboard.writeText(data.verificationCode);
                        } catch (error) {

                        }

                    } else {
                        showError('查询失败: ' + (data.message || '未知错误'));
                    }
                } catch (error) {
                    // 隐藏加载状态
                    loader.classList.remove('show');
                    submitBtn.disabled = false;

                    showError('发生错误: ' + error.message);
                }
            });

            function showError(message) {
                result.classList.add('show');
                resultContent.innerHTML = `<p class="error">${message}</p>`;
            }
        });
    </script>
</body>
</html>
